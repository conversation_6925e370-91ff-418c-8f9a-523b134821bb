<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Hvac08DeviceFull</class>
 <widget class="QWidget" name="Hvac08DeviceFull">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1223</width>
    <height>728</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">/* 工业风格样式表 */

/* 主窗口背景 */
QWidget {
	background-color: #2b2b2b;
	color: #ffffff;
	font-family: "Microsoft YaHei", Arial, sans-serif;
}

/* QGroupBox 工业风格 */
QGroupBox {
	font-size: 14px;
	font-weight: bold;
	color: #ffffff;
	border: 3px solid #4a90e2;
	border-radius: 8px;
	margin-top: 10px;
	padding-top: 5px;
	background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
		stop:0 #3a3a3a, stop:0.1 #404040,
		stop:0.5 #353535, stop:0.9 #2a2a2a, stop:1 #1f1f1f);
	box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.3);
}

QGroupBox::title {
	subcontrol-origin: margin;
	subcontrol-position: top left;
	left: 15px;
	top: -5px;
	color: #4a90e2;
	font-weight: bold;
	background-color: transparent;
	padding: 0 8px;
}

/* QCheckBox 样式 */
QCheckBox {
	min-width:60px;
	min-height:20px;
	max-height:20px;
	color: #ffffff;
	font-size: 12px;
}

QCheckBox::indicator::unchecked {
	image: url(:/image/widget/checkbox_uncheck.png);
}

QCheckBox::indicator::checked {
	image: url(:/image/widget/checkbox_checked.png);
}

/* QLineEdit 工业风格 */
QLineEdit {
	font-size: 14px;
	min-height: 25px;
	background-color: #1a1a1a;
	border: 2px solid #555555;
	border-radius: 4px;
	padding: 2px 5px;
	color: #ffffff;
	selection-background-color: #4a90e2;
}

QLineEdit:focus {
	border: 2px solid #4a90e2;
	background-color: #252525;
}

QLineEdit:read-only {
	background-color: #333333;
	color: #cccccc;
}

/* QPushButton 工业风格 */
QPushButton {
	background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
		stop:0 #4a90e2, stop:1 #357abd);
	border: 2px solid #357abd;
	border-radius: 6px;
	color: #ffffff;
	font-weight: bold;
	padding: 5px 10px;
	min-height: 25px;
}

QPushButton:hover {
	background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
		stop:0 #5ba0f2, stop:1 #4a90e2);
	border: 2px solid #4a90e2;
}

QPushButton:pressed {
	background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
		stop:0 #357abd, stop:1 #2a5f8f);
}

/* QLabel 样式 */
QLabel {
	color: #ffffff;
	font-size: 12px;
}

/* Line 控件 - 总线工业风格 */
Line {
	background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
		stop:0 #ff6b35, stop:0.5 #ff8c42, stop:1 #ff6b35);
	border: none;
	border-radius: 2px;
}

/* 通过名称指定特定的Line样式 */
#LIE_CAN_3, #LIE_CAN_4, #LIE_CAN_5, #LIE_CAN_6 {
	background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
		stop:0 #ff6b35, stop:0.5 #ff8c42, stop:1 #ff6b35);
	min-height: 5px;
	max-height: 5px;
	border-radius: 2px;
}

#LIE_XT4, #LIE_CAN2_Exio02_1, #LIE_CAN2_Exio02_2, #LIE_CAN2_Exio02_3, #LIE_CAN2_Exio02_4 {
	background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
		stop:0 #ff6b35, stop:0.5 #ff8c42, stop:1 #ff6b35);
	min-width: 5px;
	max-width: 5px;
	border-radius: 2px;
}

/* 状态指示灯样式 */
#LB_hvac08_nand,#LB_hvac08_nor,
#LB_hvac08_io_nor,
#LB_exio01_1_nor,#LB_exio01_2_nor,
#LB_exio02_1_nor,#LB_exio02_2_nor,
#LB_cbm01_1_nor,#LB_cbm01_2_nor,
#LB_cbm02_1_nor,#LB_cbm02_2_nor,
#LB_rs485_0_en,
#LB_rs485_1_en,
#LB_rs485_2_en {
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background: qradialgradient(cx:0.5, cy:0.5, radius:0.8,
		stop:0 #ffffff, stop:0.7 #e0e0e0, stop:1 #8B8989);
	border:2px solid #555555;
	box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.3);
}

</string>
  </property>
  <widget class="QGroupBox" name="groupBox">
   <property name="geometry">
    <rect>
     <x>375</x>
     <y>40</y>
     <width>561</width>
     <height>231</height>
    </rect>
   </property>
   <property name="title">
    <string/>
   </property>
   <layout class="QGridLayout" name="gridLayout_3" columnstretch="4,3">
    <property name="leftMargin">
     <number>5</number>
    </property>
    <property name="topMargin">
     <number>5</number>
    </property>
    <property name="rightMargin">
     <number>5</number>
    </property>
    <property name="bottomMargin">
     <number>5</number>
    </property>
    <property name="spacing">
     <number>5</number>
    </property>
    <item row="0" column="0" rowspan="2">
     <widget class="QGroupBox" name="GB_hvac08">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>HVAC</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_2">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="0" column="0">
        <widget class="QLabel" name="LB_hvac08_nand_lab">
         <property name="text">
          <string>Nand Flash</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLabel" name="LB_hvac08_nand">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="QLabel" name="LB_hvac08_nor">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QLabel" name="LB_hvac08_nor_lab">
         <property name="text">
          <string>Nor Flash</string>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="LB_hvac08_cpld">
         <property name="text">
          <string>CPLD:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_hvac08_app">
         <property name="text">
          <string>应用版本:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="LB_hvac08_id">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3" colspan="2">
        <widget class="QLineEdit" name="LE_hvac08_id"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_hvac08_fw">
         <property name="text">
          <string>底层版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="LB_hvac08_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_hvac08_app"/>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_hvac08_fw"/>
       </item>
       <item row="2" column="3" colspan="2">
        <widget class="QLineEdit" name="LE_hvac08_5v"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_hvac08_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="LE_hvac08_boot"/>
       </item>
       <item row="3" column="3" colspan="2">
        <widget class="QLineEdit" name="LE_hvac08_cpld"/>
       </item>
       <item row="4" column="3" rowspan="2" colspan="2">
        <widget class="QPushButton" name="PB_hvac08_rtc">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>同步时钟</string>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="LB_have_ip">
         <property name="text">
          <string>IP地址:</string>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="LB_hvac08_rtc">
         <property name="text">
          <string>RTC时钟:</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1" colspan="2">
        <widget class="QLineEdit" name="LE_hvac08_ip">
         <property name="text">
          <string>***************</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1" colspan="2">
        <widget class="QLineEdit" name="LE_hvac08_rtc">
         <property name="text">
          <string>2025-7-28 17:44:13</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QGroupBox" name="GB_hvac08_io">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>191</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>HVAC08-IO</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_9">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="2" column="4">
        <widget class="QLineEdit" name="LE_hvac08_io_24v"/>
       </item>
       <item row="2" column="3">
        <widget class="QLabel" name="LB_hvac08_io_24v">
         <property name="text">
          <string>板内24V:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_hvac08_io_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_hvac08_io_fw"/>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_hvac08_io_boot"/>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_hvac08_io_fw">
         <property name="text">
          <string>底层版本:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLabel" name="LB_hvac08_io_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="4">
        <widget class="QLineEdit" name="LE_hvac08_io_5v"/>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="LB_hvac08_io_lab">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="LE_hvac08_io_id"/>
       </item>
       <item row="0" column="4">
        <widget class="QLabel" name="LB_hvac08_io_nor">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QLabel" name="LB_hvac08_io_nor_lab">
         <property name="text">
          <string>Nor Flash</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QGroupBox" name="GB_mvb">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>191</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>MVB</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_5" columnstretch="0,0,0,0">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="0" column="2">
        <widget class="QLabel" name="LB_mvb_fw">
         <property name="text">
          <string>底层软件:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="LB_mvb_app">
         <property name="text">
          <string>应用版本:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="LE_mvb_app"/>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_mvb_boot"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_mvb_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QLineEdit" name="LE_mvb_fw"/>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="Line" name="LIE_XT4">
   <property name="geometry">
    <rect>
     <x>489</x>
     <y>270</y>
     <width>223</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Plain</enum>
   </property>
   <property name="lineWidth">
    <number>5</number>
   </property>
   <property name="orientation">
    <enum>Qt::Vertical</enum>
   </property>
   <property name="online" stdset="0">
    <number>0</number>
   </property>
  </widget>
  <widget class="Line" name="LIE_CAN2_Exio02_1">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>303</y>
     <width>223</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Plain</enum>
   </property>
   <property name="lineWidth">
    <number>5</number>
   </property>
   <property name="midLineWidth">
    <number>3</number>
   </property>
   <property name="orientation">
    <enum>Qt::Vertical</enum>
   </property>
   <property name="online" stdset="0">
    <number>0</number>
   </property>
  </widget>
  <widget class="Line" name="LIE_CAN2_Exio02_2">
   <property name="geometry">
    <rect>
     <x>340</x>
     <y>303</y>
     <width>223</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Plain</enum>
   </property>
   <property name="lineWidth">
    <number>5</number>
   </property>
   <property name="midLineWidth">
    <number>3</number>
   </property>
   <property name="orientation">
    <enum>Qt::Vertical</enum>
   </property>
   <property name="online" stdset="0">
    <number>0</number>
   </property>
  </widget>
  <widget class="Line" name="LIE_CAN2_Exio02_3">
   <property name="geometry">
    <rect>
     <x>630</x>
     <y>303</y>
     <width>223</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Plain</enum>
   </property>
   <property name="lineWidth">
    <number>5</number>
   </property>
   <property name="midLineWidth">
    <number>3</number>
   </property>
   <property name="orientation">
    <enum>Qt::Vertical</enum>
   </property>
   <property name="online" stdset="0">
    <number>0</number>
   </property>
  </widget>
  <widget class="Line" name="LIE_CAN2_Exio02_4">
   <property name="geometry">
    <rect>
     <x>920</x>
     <y>303</y>
     <width>223</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Plain</enum>
   </property>
   <property name="lineWidth">
    <number>5</number>
   </property>
   <property name="midLineWidth">
    <number>3</number>
   </property>
   <property name="orientation">
    <enum>Qt::Vertical</enum>
   </property>
   <property name="online" stdset="0">
    <number>0</number>
   </property>
  </widget>
  <widget class="Line" name="LIE_CAN_3">
   <property name="geometry">
    <rect>
     <x>31</x>
     <y>300</y>
     <width>1139</width>
     <height>5</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Plain</enum>
   </property>
   <property name="lineWidth">
    <number>5</number>
   </property>
   <property name="midLineWidth">
    <number>5</number>
   </property>
   <property name="orientation">
    <enum>Qt::Horizontal</enum>
   </property>
   <property name="online" stdset="0">
    <number>0</number>
   </property>
  </widget>
  <widget class="Line" name="LIE_CAN_4">
   <property name="geometry">
    <rect>
     <x>263</x>
     <y>80</y>
     <width>110</width>
     <height>5</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Plain</enum>
   </property>
   <property name="lineWidth">
    <number>5</number>
   </property>
   <property name="midLineWidth">
    <number>5</number>
   </property>
   <property name="orientation">
    <enum>Qt::Horizontal</enum>
   </property>
   <property name="online" stdset="0">
    <number>0</number>
   </property>
  </widget>
  <widget class="Line" name="LIE_CAN_5">
   <property name="geometry">
    <rect>
     <x>261</x>
     <y>158</y>
     <width>110</width>
     <height>5</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Plain</enum>
   </property>
   <property name="lineWidth">
    <number>5</number>
   </property>
   <property name="midLineWidth">
    <number>5</number>
   </property>
   <property name="orientation">
    <enum>Qt::Horizontal</enum>
   </property>
   <property name="online" stdset="0">
    <number>0</number>
   </property>
  </widget>
  <widget class="Line" name="LIE_CAN_6">
   <property name="geometry">
    <rect>
     <x>260</x>
     <y>230</y>
     <width>110</width>
     <height>5</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Plain</enum>
   </property>
   <property name="lineWidth">
    <number>5</number>
   </property>
   <property name="midLineWidth">
    <number>5</number>
   </property>
   <property name="orientation">
    <enum>Qt::Horizontal</enum>
   </property>
   <property name="online" stdset="0">
    <number>0</number>
   </property>
  </widget>
  <widget class="QWidget" name="">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>201</y>
     <width>201</width>
     <height>61</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout_16" columnstretch="3,1,1">
    <item row="0" column="0" rowspan="2">
     <widget class="QGroupBox" name="GB_unit2">
      <property name="title">
       <string>机组2</string>
      </property>
     </widget>
    </item>
    <item row="0" column="1" colspan="2">
     <widget class="QCheckBox" name="CB_RS485_2_En">
      <property name="text">
       <string>使能</string>
      </property>
      <property name="checked">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QLabel" name="LB_rs485_2_en">
      <property name="text">
       <string/>
      </property>
      <property name="en" stdset="0">
       <number>0</number>
      </property>
     </widget>
    </item>
    <item row="1" column="2">
     <widget class="QLabel" name="LB_rs485_2">
      <property name="text">
       <string>RS485-2</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>130</y>
     <width>201</width>
     <height>61</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout_17" columnstretch="3,1,1">
    <item row="0" column="0" rowspan="2">
     <widget class="QGroupBox" name="GB_unit1">
      <property name="title">
       <string>机组1</string>
      </property>
     </widget>
    </item>
    <item row="0" column="1" colspan="2">
     <widget class="QCheckBox" name="CB_RS485_1_En">
      <property name="text">
       <string>使能</string>
      </property>
      <property name="checked">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QLabel" name="LB_rs485_1_en">
      <property name="text">
       <string/>
      </property>
      <property name="en" stdset="0">
       <number>0</number>
      </property>
     </widget>
    </item>
    <item row="1" column="2">
     <widget class="QLabel" name="LB_rs485_1">
      <property name="text">
       <string>RS485-1</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>50</y>
     <width>201</width>
     <height>71</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout_18" columnstretch="3,1,1">
    <item row="0" column="0" rowspan="2">
     <widget class="QGroupBox" name="GB_hmi">
      <property name="title">
       <string>HMI</string>
      </property>
     </widget>
    </item>
    <item row="0" column="1" colspan="2">
     <widget class="QCheckBox" name="CB_RS485_0_En">
      <property name="text">
       <string>使能</string>
      </property>
      <property name="checked">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QLabel" name="LB_rs485_0_en">
      <property name="text">
       <string/>
      </property>
      <property name="en" stdset="0">
       <number>0</number>
      </property>
     </widget>
    </item>
    <item row="1" column="2">
     <widget class="QLabel" name="LB_rs485_0">
      <property name="text">
       <string>RS485-0</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="">
   <property name="geometry">
    <rect>
     <x>31</x>
     <y>327</y>
     <width>1139</width>
     <height>312</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout_15">
    <item row="0" column="0">
     <widget class="QGroupBox" name="GB_exio01_1">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>EXIO01-1</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_exio01_1_app"/>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_exio01_1_app">
         <property name="text">
          <string>应用版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_exio01_1_fw">
         <property name="text">
          <string>底层版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_exio01_1_fw"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_exio01_1_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="LE_exio01_1_boot"/>
       </item>
       <item row="0" column="0" colspan="4">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="LB_exio01_1_nor_lab">
           <property name="text">
            <string>Nor:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_exio01_1_nor">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="LB_exio01_1_id_lab">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLineEdit" name="LE_exio01_1_id"/>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="LB_exio01_1_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QLineEdit" name="LE_exio01_1_5v"/>
       </item>
      </layout>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QGroupBox" name="GB_exio02_1">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>EXIO02-1</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_6">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_exio02_1_app">
         <property name="text">
          <string>应用版本:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_exio02_1_app"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_exio02_1_fw">
         <property name="text">
          <string>底层版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_exio02_1_fw"/>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="LE_exio02_1_boot"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_exio02_1_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0" colspan="4">
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="LB_exio02_1_nor_lab">
           <property name="text">
            <string>Nor:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_exio02_1_nor">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="LB_exio02_1_id_lab">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLineEdit" name="LE_exio02_1_id"/>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="LB_exio02_1_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QLineEdit" name="LE_exio02_1_5v"/>
       </item>
      </layout>
     </widget>
    </item>
    <item row="0" column="2">
     <widget class="QGroupBox" name="GB_cbm01_1">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>CBM01-1</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_4">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="0" column="0" colspan="4">
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <spacer name="horizontalSpacer_8">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>45</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="LB_cbm01_1_nor_lab">
           <property name="text">
            <string>Nor Flash</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_cbm01_1_nor">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_cbm01_1_fw"/>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="LB_cbm01_1_id">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_cbm01_1_fw">
         <property name="text">
          <string>底层版本</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLineEdit" name="LE_cbm01_1_id"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_cbm01_1_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_cbm01_1_boot"/>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="LB_cbm01_1_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="3">
        <widget class="QLineEdit" name="LE_cbm01_1_ad2"/>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="LE_cbm01_1_ad1"/>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="LB_cbm01_1_ad2">
         <property name="text">
          <string>AD2版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QLineEdit" name="LE_cbm01_1_5v"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_cbm01_1_ad1">
         <property name="text">
          <string>AD1版本:</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="0" column="3">
     <widget class="QGroupBox" name="GB_cbm02_1">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>CBM02-1</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_10">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="1" column="2">
        <widget class="QLabel" name="LB_cbm02_1_id">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0" colspan="4">
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <item>
          <spacer name="horizontalSpacer_11">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>45</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="LB_cbm02_1_nor_lab">
           <property name="text">
            <string>Nor Flash</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_cbm02_1_nor">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_cbm02_1_fw"/>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_cbm02_1_fw">
         <property name="text">
          <string>底层版本</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLineEdit" name="LE_cbm02_1_id"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_cbm02_1_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="LE_cbm02_1_ad1"/>
       </item>
       <item row="3" column="3">
        <widget class="QLineEdit" name="LE_cbm02_1_ad2"/>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="LB_cbm02_1_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_cbm02_1_boot"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_cbm02_1_ad1">
         <property name="text">
          <string>AD1版本:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="LB_cbm02_1_ad2">
         <property name="text">
          <string>AD2版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QLineEdit" name="LE_cbm02_1_5v"/>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QGroupBox" name="GB_exio01_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>EXIO01-2</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_11">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_exio01_2_app">
         <property name="text">
          <string>应用版本:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_exio01_2_app"/>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="LE_exio01_2_boot"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_exio01_2_fw">
         <property name="text">
          <string>底层版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_exio01_2_fw"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_exio01_2_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0" colspan="4">
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <spacer name="horizontalSpacer_5">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="LB_exio01_2_nor_lab">
           <property name="text">
            <string>Nor:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_exio01_2_nor">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="LB_exio01_2_id_lab">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLineEdit" name="LE_exio01_2_id"/>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="LB_exio01_2_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QLineEdit" name="LE_exio01_2_5v"/>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QGroupBox" name="GB_exio02_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>EXIO02-2</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_12">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_exio02_2_app">
         <property name="text">
          <string>应用版本:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_exio02_2_app"/>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_exio02_2_fw"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_exio02_2_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="LE_exio02_2_boot"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_exio02_2_fw">
         <property name="text">
          <string>底层版本:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0" colspan="4">
        <layout class="QHBoxLayout" name="horizontalLayout_8">
         <item>
          <spacer name="horizontalSpacer_7">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="LB_exio02_2_nor_lab">
           <property name="text">
            <string>Nor:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_exio02_2_nor">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="LB_exio02_2_id_lab">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLineEdit" name="LE_exio02_2_id"/>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="LB_exio02_2_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QLineEdit" name="LE_exio02_2_5v"/>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="2">
     <widget class="QGroupBox" name="GB_cbm01_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>CBM01-2</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_13">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="0" column="0" colspan="4">
        <layout class="QHBoxLayout" name="horizontalLayout_9">
         <item>
          <spacer name="horizontalSpacer_13">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>45</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="LB_cbm01_2_nor_lab">
           <property name="text">
            <string>Nor Flash</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_cbm01_2_nor">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_cbm01_2_fw"/>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="LB_cbm01_2_id">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_cbm01_2_fw">
         <property name="text">
          <string>底层版本</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLineEdit" name="LE_cbm01_2_id"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_cbm01_2_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_cbm01_2_boot"/>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="LB_cbm01_2_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="3">
        <widget class="QLineEdit" name="LE_cbm01_2_ad2"/>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="LE_cbm01_2_ad1"/>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="LB_cbm01_2_ad2">
         <property name="text">
          <string>AD2版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QLineEdit" name="LE_cbm01_2_5v"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_cbm01_2_ad1">
         <property name="text">
          <string>AD1版本:</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="3">
     <widget class="QGroupBox" name="GB_cbm02_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string>CBM02-2</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QGridLayout" name="gridLayout_14">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>5</number>
       </property>
       <item row="0" column="0" colspan="4">
        <layout class="QHBoxLayout" name="horizontalLayout_10">
         <item>
          <spacer name="horizontalSpacer_14">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>45</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="LB_cbm02_2_nor_lab">
           <property name="text">
            <string>Nor Flash</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_cbm02_2_nor">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="LE_cbm02_2_fw"/>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="LB_cbm02_2_id">
         <property name="text">
          <string>板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_cbm02_2_fw">
         <property name="text">
          <string>底层版本</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLineEdit" name="LE_cbm02_2_id"/>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="LB_cbm02_2_boot">
         <property name="text">
          <string>Boot版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLineEdit" name="LE_cbm02_2_boot"/>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="LB_cbm02_2_5v">
         <property name="text">
          <string>板内5V:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="3">
        <widget class="QLineEdit" name="LE_cbm02_2_ad2"/>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="LE_cbm02_2_ad1"/>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="LB_cbm02_2_ad2">
         <property name="text">
          <string>AD2版本:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QLineEdit" name="LE_cbm02_2_5v"/>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_cbm02_2_ad1">
         <property name="text">
          <string>AD1版本:</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
